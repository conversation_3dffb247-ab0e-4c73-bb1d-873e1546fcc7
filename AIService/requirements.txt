aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
APScheduler==3.10.4
async-timeout==4.0.3
asyncpg==0.28.0
attrs==25.3.0
backoff==2.2.1
bcrypt==4.3.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.6.15
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
chromadb==1.0.13
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
cryptography==45.0.5
dataclasses-json==0.6.7
distro==1.9.0
durationpy==0.10
exceptiongroup==1.3.0
fastapi==0.115.14
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.7.0
fsspec==2025.5.1
google-ai-generativelanguage==0.6.18
google-api-core==2.25.1
google-api-python-client==2.176.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-genai==1.30.0
googleapis-common-protos==1.70.0
greenlet==3.2.3
grpcio==1.73.1
grpcio-status==1.71.2
h11==0.16.0
hf-xet==1.1.5
html2docx==1.6.0
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.33.1
humanfriendly==10.0
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
iniconfig==2.1.0
jiter==0.10.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kubernetes==33.1.0
langchain==0.3.26
langchain-chroma==0.2.4
langchain-community==0.3.26
langchain-core==0.3.66
langchain-experimental==0.3.4
langchain-google-genai==2.1.6
langchain-ollama==0.3.3
langchain-openai==0.3.27
langchain-text-splitters==0.3.8
langgraph==0.6.2
langgraph-checkpoint==2.1.1
langgraph-prebuilt==0.6.2
langgraph-sdk==0.2.0
langsmith==0.4.4
loguru==0.7.2
lxml==6.0.0
Markdown==3.5.1
markdown-it-py==3.0.0
marshmallow==3.26.1
mdurl==0.1.2
mmh3==5.1.0
mpmath==1.3.0
multidict==6.6.3
mypy_extensions==1.1.0
numpy==2.0.2
oauthlib==3.3.1
ollama==0.5.1
onnxruntime==1.19.2
openai==1.93.0
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
orjson==3.10.18
ormsgpack==1.10.0
overrides==7.7.0
packaging==24.2
pgvector==0.4.1
pillow==10.3.0
pluggy==1.6.0
posthog==6.0.1
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
psycopg2-binary==2.9.9
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.1
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
Pygments==2.19.2
PyMuPDF==1.24.4
PyMuPDFb==1.24.3
pypandoc==1.15
pyparsing==3.2.3
PyPDF2==3.0.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pytest==8.4.1
python-dateutil==2.9.0.post0
python-docx==1.1.0
python-dotenv==1.0.0
pytz==2025.2
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
reportlab==4.2.5
requests==2.32.4
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.26.0
rsa==4.9.1
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
starlette==0.46.2
sympy==1.14.0
tenacity==9.1.2
tiktoken==0.9.0
tinycss2==1.4.0
tokenizers==0.21.2
tomli==2.2.1
tqdm==4.67.1
typer==0.16.0
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
tzlocal==5.3.1
uritemplate==4.2.0
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
watchfiles==1.1.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
win32_setctime==1.2.0
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0
