"""
Document Title Extraction Service

This service uses LLM to intelligently extract document titles and submission requirements
from solicitation data, providing meaningful names for generated proposal documents.
"""

import json
import re
from typing import Dict, List, Optional, Any
from loguru import logger

from utils.llm import LLMFactory


class DocumentTitleExtractionService:
    """Service for extracting intelligent document titles from solicitation data"""

    def __init__(self):
        self.llm = LLMFactory.get_llm()
    
    async def extract_document_title(
        self,
        opportunity_data: Dict[str, Any],
        volume_number: int,
        volume_title: str,
        source: str = "custom"
    ) -> str:
        """
        Extract intelligent document title from opportunity data using LLM
        
        Args:
            opportunity_data: Dictionary containing opportunity information
            volume_number: Volume number (1-5)
            volume_title: Current volume title from TOC
            source: Source type (custom, sam, ebuy)
            
        Returns:
            Intelligent document title string
        """
        try:
            # Prepare opportunity context for LLM
            context = self._prepare_opportunity_context(opportunity_data, source)
            
            # Create prompt for title extraction
            prompt = self._create_title_extraction_prompt(
                context, volume_number, volume_title
            )
            
            # Get LLM response
            response = await self.llm.generate_response(
                prompt=prompt,
                max_tokens=150,
                temperature=0.3
            )
            
            # Extract and clean the title
            extracted_title = self._clean_extracted_title(response, volume_title)
            
            logger.info(f"Extracted document title for volume {volume_number}: {extracted_title}")
            return extracted_title
            
        except Exception as e:
            logger.error(f"Error extracting document title: {e}")
            # Fallback to volume title
            return self._create_fallback_title(volume_title, volume_number)
    
    def _prepare_opportunity_context(
        self, 
        opportunity_data: Dict[str, Any], 
        source: str
    ) -> str:
        """Prepare opportunity context for LLM analysis"""
        try:
            context_parts = []
            
            # Add title and description
            if hasattr(opportunity_data, 'title') and opportunity_data.title:
                context_parts.append(f"Opportunity Title: {opportunity_data.title}")
            
            if hasattr(opportunity_data, 'description') and opportunity_data.description:
                # Truncate description if too long
                description = str(opportunity_data.description)[:1000]
                context_parts.append(f"Description: {description}")
            
            # Add solicitation number if available
            if hasattr(opportunity_data, 'solicitation_number') and opportunity_data.solicitation_number:
                context_parts.append(f"Solicitation Number: {opportunity_data.solicitation_number}")
            
            # Add agency information
            if hasattr(opportunity_data, 'full_parent_path_name') and opportunity_data.full_parent_path_name:
                context_parts.append(f"Agency: {opportunity_data.full_parent_path_name}")
            
            # Add NAICS code
            if hasattr(opportunity_data, 'naics_code') and opportunity_data.naics_code:
                context_parts.append(f"NAICS Code: {opportunity_data.naics_code}")
            
            # Add opportunity type
            if hasattr(opportunity_data, 'opportunity_type') and opportunity_data.opportunity_type:
                context_parts.append(f"Type: {opportunity_data.opportunity_type}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing opportunity context: {e}")
            return "Limited opportunity information available"
    
    def _create_title_extraction_prompt(
        self, 
        context: str, 
        volume_number: int, 
        volume_title: str
    ) -> str:
        """Create prompt for LLM title extraction"""
        return f"""
You are an expert in government contracting and proposal writing. Based on the following solicitation information, extract an intelligent and professional document title for Volume {volume_number} ({volume_title}) of a proposal response.

Solicitation Information:
{context}

Current Volume: Volume {volume_number} - {volume_title}

Instructions:
1. Analyze the solicitation to understand what type of document submission is required
2. Look for specific document naming requirements or submission instructions
3. Consider the volume type and typical government proposal structure
4. Create a professional, descriptive title that would be appropriate for this volume
5. The title should be concise (under 60 characters) and professional
6. If specific naming requirements are mentioned in the solicitation, follow them
7. If no specific requirements, use standard government proposal naming conventions

Common volume types and their typical titles:
- Volume I: Technical Capability, Technical Approach, Technical Proposal
- Volume II: Price Proposal, Cost Proposal, Pricing
- Volume III: Management Approach, Management Plan
- Volume IV: Past Performance, Corporate Experience
- Volume V: Additional Requirements, Appendices

Respond with ONLY the document title, nothing else. Do not include "Volume" or volume numbers in the title unless specifically required by the solicitation.

Document Title:"""
    
    def _clean_extracted_title(self, llm_response: str, fallback_title: str) -> str:
        """Clean and validate the extracted title"""
        try:
            # Extract the title from LLM response
            title = llm_response.strip()
            
            # Remove common prefixes/suffixes
            title = re.sub(r'^(Document Title:|Title:|Response:)\s*', '', title, flags=re.IGNORECASE)
            title = re.sub(r'\s*(Document|Title)\s*$', '', title, flags=re.IGNORECASE)
            
            # Remove quotes if present
            title = title.strip('"\'')
            
            # Validate length
            if len(title) > 80:
                title = title[:77] + "..."
            
            # Validate content
            if len(title) < 3 or not title.replace(' ', '').replace('-', '').replace('_', '').isalnum():
                return self._create_fallback_title(fallback_title, 1)
            
            # Clean for filename use
            title = re.sub(r'[<>:"/\\|?*]', '', title)
            title = re.sub(r'\s+', ' ', title).strip()
            
            return title
            
        except Exception as e:
            logger.error(f"Error cleaning extracted title: {e}")
            return self._create_fallback_title(fallback_title, 1)
    
    def _create_fallback_title(self, volume_title: str, volume_number: int) -> str:
        """Create fallback title when extraction fails"""
        # Clean volume title
        clean_title = volume_title.replace('_', ' ').replace('-', ' ')
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        # Remove "Volume" prefix if present
        clean_title = re.sub(r'^Volume\s+\w+\s*[-:]?\s*', '', clean_title, flags=re.IGNORECASE)
        
        if clean_title and len(clean_title) > 3:
            return clean_title
        else:
            # Ultimate fallback
            volume_names = {
                1: "Technical Proposal",
                2: "Price Proposal", 
                3: "Management Approach",
                4: "Past Performance",
                5: "Additional Requirements"
            }
            return volume_names.get(volume_number, f"Proposal Volume {volume_number}")
    
    async def extract_submission_requirements(
        self,
        opportunity_data: Dict[str, Any],
        source: str = "custom"
    ) -> Dict[str, Any]:
        """
        Extract submission requirements and document specifications from solicitation
        
        Returns:
            Dictionary containing submission requirements like format, naming, etc.
        """
        try:
            context = self._prepare_opportunity_context(opportunity_data, source)
            
            prompt = f"""
You are an expert in government contracting. Analyze the following solicitation information and extract any specific document submission requirements.

Solicitation Information:
{context}

Extract the following information if mentioned:
1. Document format requirements (PDF, DOCX, etc.)
2. File naming conventions
3. Page limits or formatting requirements
4. Specific document titles required
5. Submission deadlines
6. Any special instructions for proposal format

Respond in JSON format with the following structure:
{{
    "format_requirements": "string describing format requirements",
    "naming_convention": "string describing naming requirements", 
    "page_limits": "string describing page limits",
    "document_titles": ["list", "of", "required", "document", "titles"],
    "special_instructions": "string with any special formatting instructions"
}}

If no specific requirements are found for a field, use null.

JSON Response:"""
            
            response = await self.llm_service.generate_response(
                prompt=prompt,
                max_tokens=300,
                temperature=0.2
            )
            
            # Parse JSON response
            try:
                requirements = json.loads(response.strip())
                logger.info("Successfully extracted submission requirements")
                return requirements
            except json.JSONDecodeError:
                logger.warning("Failed to parse submission requirements JSON")
                return self._get_default_requirements()
                
        except Exception as e:
            logger.error(f"Error extracting submission requirements: {e}")
            return self._get_default_requirements()
    
    def _get_default_requirements(self) -> Dict[str, Any]:
        """Get default submission requirements"""
        return {
            "format_requirements": None,
            "naming_convention": None,
            "page_limits": None,
            "document_titles": None,
            "special_instructions": None
        }
