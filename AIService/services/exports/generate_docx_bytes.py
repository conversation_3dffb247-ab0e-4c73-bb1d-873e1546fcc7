"""
DOCX Bytes Generator Service

This service generates DOCX documents as bytes for storage in the database,
similar to the PDF bytes generator but for DOCX format.
"""

import os
import tempfile
from io import BytesIO
from pathlib import Path
from typing import Dict, List, Optional, Any
from loguru import logger

from models.customer_models import AESTenant, CustomOppsTable, DataMetastore, Users
from services.exports.generat_docx import DocxGenerator
from services.exports.render_html import HtmlRenderer


class DocxBytesGenerator:
    """Service for generating DOCX documents as bytes"""
    
    @staticmethod
    def generate_docx_bytes(
        markdown_content: str,
        opportunity_id: str,
        tenant_id: str,
        cover_page: Optional[DataMetastore] = None,
        tenant_details: Optional[AESTenant] = None,
        opportunity_details: Optional[CustomOppsTable] = None,
        user_details: Optional[Users] = None,
        toc_data: Optional[List[Dict[str, Any]]] = None,
        trailing_page_markdown: Optional[str] = None,
        compliance: Optional[dict] = None,
        volume_number: int = 1,
        document_title: Optional[str] = None
    ) -> tuple[bytes, str]:
        """
        Generate DOCX document as bytes from markdown content.
        
        Args:
            markdown_content: The main content to convert to DOCX
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            cover_page: Optional cover page document
            tenant_details: Tenant information
            opportunity_details: Opportunity information
            user_details: User information
            toc_data: Optional table of contents data
            trailing_page_markdown: Optional trailing page content
            compliance: Optional compliance formatting settings
            volume_number: Volume number (1-5)
            document_title: Optional custom document title
            
        Returns:
            tuple[bytes, str]: (docx_bytes, success_message)
        """
        try:
            logger.info(f"Generating DOCX bytes for opportunity {opportunity_id}, volume {volume_number}")
            
            # Set default compliance settings if not provided
            compliance = compliance or {}
            font_type = compliance.get('font_type', 'Times New Roman')
            font_size_body = compliance.get('font_size_body', 12)
            font_size_header = compliance.get('font_size_header', 14)
            line_spacing = compliance.get('line_spacing', 1.15)
            
            # Prepare cover page elements if cover page is provided
            cover_page_elements = None
            if cover_page:
                cover_page_elements = {
                    'cover_page': cover_page,
                    'tenant_details': tenant_details,
                    'opportunity_details': opportunity_details,
                    'user_details': user_details,
                    'compliance': compliance,
                    'image_only': False
                }
            
            # Convert markdown to HTML first
            html_content = HtmlRenderer.render_markdown_to_html(
                markdown_content,
                font_type=font_type,
                font_size_body=font_size_body,
                font_size_header=font_size_header,
                font_size_title=font_size_header + 4,
                font_size_subheading=font_size_body,
                line_spacing=line_spacing,
            )
            
            # Add trailing page if provided
            if trailing_page_markdown:
                trailing_html = HtmlRenderer.render_markdown_to_html(
                    trailing_page_markdown,
                    font_type=font_type,
                    font_size_body=font_size_body,
                    font_size_header=font_size_header,
                    font_size_title=font_size_header + 4,
                    font_size_subheading=font_size_body,
                    line_spacing=line_spacing,
                )
                html_content = f"{html_content}\n\n<div style='page-break-before: always;'></div>\n\n{trailing_html}"
            
            # Create temporary file for DOCX generation
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                # Determine volume title text
                volume_title_text = f"Volume {volume_number}"
                if document_title:
                    volume_title_text = document_title
                elif volume_number == 1:
                    volume_title_text = "Technical Proposal"
                elif volume_number == 2:
                    volume_title_text = "Price Proposal"
                elif volume_number == 3:
                    volume_title_text = "Management Approach"
                elif volume_number == 4:
                    volume_title_text = "Past Performance"
                elif volume_number == 5:
                    volume_title_text = "Additional Requirements"
                
                # Generate DOCX file
                final_path = DocxGenerator.generate_docx_from_html(
                    html_content=html_content,
                    output_file_path=temp_path,
                    metadata={
                        "title": document_title or getattr(opportunity_details, 'title', 'Proposal Document') if opportunity_details else 'Proposal Document',
                    },
                    header_title=volume_title_text,
                    cover_page=cover_page,
                    cover_page_elements=cover_page_elements,
                    include_toc=bool(toc_data),
                    toc_title="Table of Contents",
                    toc_levels="1-2",
                    toc_data=toc_data,
                )
                
                # Read the generated DOCX file as bytes
                with open(final_path, 'rb') as docx_file:
                    docx_bytes = docx_file.read()
                
                logger.info(f"DOCX successfully generated as bytes ({len(docx_bytes)} bytes)")
                
                success_message = f"DOCX document successfully generated for volume {volume_number} ({len(docx_bytes)} bytes)"
                return docx_bytes, success_message
                
            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                    if os.path.exists(final_path) and final_path != temp_path:
                        os.unlink(final_path)
                except Exception as cleanup_error:
                    logger.warning(f"Error cleaning up temporary files: {cleanup_error}")
            
        except Exception as e:
            logger.error(f"Error generating DOCX bytes: {e}")
            raise Exception(f"Failed to generate DOCX bytes: {str(e)}")
    
    @staticmethod
    def generate_docx_with_image_only_cover(
        markdown_content: str,
        opportunity_id: str,
        tenant_id: str,
        cover_page: Optional[DataMetastore],
        tenant_details: Optional[AESTenant],
        opportunity_details: Optional[CustomOppsTable],
        user_details: Optional[Users],
        toc_data: Optional[List[Dict[str, Any]]] = None,
        trailing_page_markdown: Optional[str] = None,
        compliance: Optional[dict] = None,
        volume_number: int = 1,
        document_title: Optional[str] = None
    ) -> tuple[bytes, str]:
        """
        Generate DOCX with image-only cover page (no text overlay).
        
        Args:
            markdown_content: The main content to convert to DOCX
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            cover_page: The cover page document
            tenant_details: Tenant information
            opportunity_details: Opportunity information
            user_details: User information
            toc_data: Optional table of contents data
            trailing_page_markdown: Optional trailing page content
            compliance: Optional compliance formatting settings
            volume_number: Volume number (1-5)
            document_title: Optional custom document title
            
        Returns:
            tuple[bytes, str]: (docx_bytes, success_message)
        """
        # Prepare cover page elements with image-only mode
        cover_page_elements = None
        if cover_page:
            cover_page_elements = {
                'cover_page': cover_page,
                'tenant_details': tenant_details,
                'opportunity_details': opportunity_details,
                'user_details': user_details,
                'compliance': compliance,
                'image_only': True  # Force image-only mode
            }
        
        # Generate DOCX with image-only cover page
        return DocxBytesGenerator.generate_docx_bytes(
            markdown_content=markdown_content,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            cover_page=cover_page,
            tenant_details=tenant_details,
            opportunity_details=opportunity_details,
            user_details=user_details,
            toc_data=toc_data,
            trailing_page_markdown=trailing_page_markdown,
            compliance=compliance,
            volume_number=volume_number,
            document_title=document_title
        )


__all__ = ["DocxBytesGenerator"]
