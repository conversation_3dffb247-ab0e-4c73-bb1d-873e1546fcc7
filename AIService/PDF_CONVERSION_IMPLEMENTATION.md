# PDF Conversion Implementation for Proposal Scheduler

## Overview

This document describes the implementation of PDF conversion functionality in the proposal scheduler service. When a proposal is not set for review (`set_for_review = False`), each volume is now converted to PDF bytes using the `generate_pdf_bytes` function and saved in the `datametastore` table.

## Changes Made

### 1. Modified Proposal Scheduler Service

**File:** `AIService/services/scheduler_service/proposal_scheduler_service.py`

#### Key Changes:

1. **Updated the `set_for_review` logic** (lines 532-543):
   - When `set_for_review = True`: Volumes are moved to review queue (existing behavior)
   - When `set_for_review = False`: Volumes are converted to PDF bytes and saved to datametastore

2. **Added new method `_convert_volumes_to_pdf_bytes`** (lines 674-824):
   - Converts each proposal volume to PDF bytes
   - Saves PDF bytes to the datametastore table
   - Includes proper error handling and logging

### 2. Implementation Details

#### PDF Conversion Process:

1. **Import Required Modules:**
   ```python
   from services.exports.generate_pdf_bytes import PDFGenerator as PDFGeneratorBytes
   from services.exports.render_markdown import MarkdownRenderer
   from controllers.customer.datametastore_controller import DataMetastoreController
   ```

2. **Retrieve Necessary Data:**
   - Opportunity details (from CustomOppsTable or OppsTable based on source)
   - Tenant details (from AESTenant table)
   - User details (using system user ID 69)
   - Cover page (if cover_page_id is provided)

3. **Process Each Volume:**
   - Convert volume data to markdown using `MarkdownRenderer.convert_draft_to_markdown`
   - Get table of contents for the volume
   - Create cover page elements if cover page is available
   - Generate PDF bytes using `PDFGeneratorBytes.generate_pdf`

4. **Save to DataMetastore:**
   - Record identifier format: `{opportunity_id}_pdf_vol_{volume_number}`
   - Record type: `"PROPOSAL_PDF"`
   - Content type: `"application/pdf"`
   - File name format: `pdf_volume_{volume_number}_{opportunity_id}.pdf`

#### Parameters Used:

- **markdown_content**: Converted from volume draft data
- **opportunity_id**: The opportunity identifier
- **tenant_id**: The tenant identifier
- **cover_page_elements**: Generated from cover page if available
- **toc_data**: Table of contents for the specific volume
- **trailing_page_markdown**: None (not used in this implementation)
- **compliance**: None (uses default compliance settings)
- **volume_number**: 1-5 based on volume index
- **image_only**: False (includes text overlay on cover page)

### 3. Error Handling

The implementation includes comprehensive error handling:

- **Volume-level errors**: If one volume fails, others continue processing
- **Database errors**: Proper rollback and error logging
- **PDF generation errors**: Detailed error messages and stack traces
- **Missing data handling**: Graceful handling of missing opportunity/tenant/user data

### 4. Database Integration

#### DataMetastore Table Structure:
- `record_identifier`: Unique identifier for the PDF record
- `record_type`: Set to "PROPOSAL_PDF"
- `tenant_id`: Associated tenant
- `original_document_content_type`: "application/pdf"
- `original_document_file_name`: Descriptive filename
- `original_document`: PDF bytes (LargeBinary field)
- `owner`: User who submitted the job

#### Update vs Insert Logic:
- Checks if record already exists using `get_by_record_identifier`
- Updates existing record if found
- Creates new record if not found

### 5. Testing

A test script has been created: `AIService/test_pdf_conversion.py`

The test:
- Creates mock volume data with sample content
- Creates mock table of contents data
- Calls the PDF conversion method
- Verifies records are created in the datametastore
- Provides detailed logging of the process

## Usage

The PDF conversion is automatically triggered when:

1. A proposal is processed by the scheduler service
2. The job instruction has `"setForReview": false`
3. Proposal volumes have been successfully generated

## Benefits

1. **Immediate PDF Availability**: PDFs are generated immediately after proposal creation
2. **No Queue Dependency**: Bypasses the format queue for faster processing
3. **Consistent Storage**: Uses the same datametastore table as other PDF exports
4. **Error Resilience**: Individual volume failures don't stop the entire process
5. **Audit Trail**: Complete logging of PDF generation process

## Future Enhancements

Potential improvements could include:

1. **Batch Processing**: Process multiple volumes in parallel
2. **Compression**: Compress PDF bytes before storage
3. **Metadata Storage**: Store additional metadata about the PDF generation
4. **Cleanup**: Implement cleanup of old PDF records
5. **Validation**: Add PDF validation before storage

## Dependencies

The implementation relies on:

- `PDFGenerator` from `generate_pdf_bytes.py`
- `MarkdownRenderer` from `render_markdown.py`
- `DataMetastoreController` for database operations
- Various model controllers for data retrieval
- Existing database connection management

## Configuration

No additional configuration is required. The implementation uses existing:

- Database connections
- PDF generation settings
- Logging configuration
- Error handling patterns
