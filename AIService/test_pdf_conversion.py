#!/usr/bin/env python3
"""
Test script to verify the PDF conversion functionality in proposal scheduler service.
This script tests the _convert_volumes_to_pdf_bytes method.
"""

import asyncio
import json
import sys
import os
from typing import List, Dict, Any, Optional

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from database import get_customer_db

async def test_document_conversion():
    """Test both PDF and DOCX conversion functionality"""
    try:
        # Import the scheduler service
        from services.scheduler_service.proposal_scheduler_service import ProposalSchedulerService
        
        # Create a test instance
        scheduler = ProposalSchedulerService()
        
        # Create mock data for testing
        mock_volumes = [
            [
                {
                    "title": "Executive Summary",
                    "content": "This is a test executive summary for volume 1. It contains important information about our proposal approach and key benefits."
                },
                {
                    "title": "Technical Approach", 
                    "content": "Our technical approach leverages cutting-edge technologies and proven methodologies to deliver exceptional results."
                }
            ],
            [
                {
                    "title": "Cost Analysis",
                    "content": "This section provides a detailed breakdown of project costs and pricing structure."
                },
                {
                    "title": "Pricing Schedule",
                    "content": "The pricing schedule outlines our competitive rates and payment terms."
                }
            ]
        ]
        
        mock_toc = [
            [
                {"title": "Executive Summary", "page_limit": 2},
                {"title": "Technical Approach", "page_limit": 5}
            ],
            [
                {"title": "Cost Analysis", "page_limit": 3},
                {"title": "Pricing Schedule", "page_limit": 2}
            ]
        ]
        
        # Test parameters
        tenant_id = "test-tenant-123"
        opps_id = "test-opportunity-456"
        source = "custom"
        client_short_name = "TestClient"
        cover_page_id = None  # No cover page for this test
        job_submitted_by = "test-user"
        
        logger.info("Starting document conversion tests...")

        # Test PDF conversion (export_type = 2)
        logger.info("Testing PDF conversion...")
        await scheduler._convert_volumes_to_pdf_bytes(
            all_volumes=mock_volumes,
            all_table_of_contents=mock_toc,
            tenant_id=tenant_id,
            opps_id=opps_id,
            source=source,
            client_short_name=client_short_name,
            cover_page_id=cover_page_id,
            job_submitted_by=job_submitted_by
        )

        # Test DOCX conversion (export_type = 1)
        logger.info("Testing DOCX conversion...")
        await scheduler._convert_volumes_to_docx_bytes(
            all_volumes=mock_volumes,
            all_table_of_contents=mock_toc,
            tenant_id=tenant_id,
            opps_id=opps_id,
            source=source,
            client_short_name=client_short_name,
            cover_page_id=cover_page_id,
            job_submitted_by=job_submitted_by
        )

        logger.info("✅ Document conversion tests completed successfully!")

        # Verify the records were created in datametastore
        from controllers.customer.datametastore_controller import DataMetastoreController

        async for db in get_customer_db():
            for volume_num in [1, 2]:
                # Check PDF records
                pdf_record_identifier = f"{opps_id}_pdf_vol_{volume_num}"
                pdf_record = await DataMetastoreController.get_by_record_identifier(db, pdf_record_identifier)

                if pdf_record:
                    logger.info(f"✅ Found PDF record for volume {volume_num}: {pdf_record.id} ({len(pdf_record.original_document)} bytes)")
                else:
                    logger.warning(f"❌ No PDF record found for volume {volume_num}")

                # Check DOCX records
                docx_record_identifier = f"{opps_id}_docx_vol_{volume_num}"
                docx_record = await DataMetastoreController.get_by_record_identifier(db, docx_record_identifier)

                if docx_record:
                    logger.info(f"✅ Found DOCX record for volume {volume_num}: {docx_record.id} ({len(docx_record.original_document)} bytes)")
                else:
                    logger.warning(f"❌ No DOCX record found for volume {volume_num}")
            break
        
        return True
        
    except Exception as e:
        logger.error(f"❌ PDF conversion test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main test function"""
    logger.info("🧪 Starting document conversion test suite (PDF & DOCX)...")

    try:
        # Run the test
        success = await test_document_conversion()

        if success:
            logger.info("🎉 All tests passed!")
        else:
            logger.error("💥 Tests failed!")

    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())
